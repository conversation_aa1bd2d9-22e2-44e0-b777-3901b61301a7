<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <!-- Filter Section -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_filters"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Filtros de Búsqueda"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:layout_marginBottom="12dp" />

            <!-- Language Filter -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Idiomas"
                android:textSize="14sp"
                android:textColor="@color/black"
                android:layout_marginBottom="8dp" />

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/chip_group_languages"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_spanish"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Español"
                    android:checkable="true" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_english"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Inglés"
                    android:checkable="true" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_french"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Francés"
                    android:checkable="true" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_german"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Alemán"
                    android:checkable="true" />

            </com.google.android.material.chip.ChipGroup>

            <!-- Availability Filter -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_date_from"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:hint="Desde">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_date_from"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="date"
                        android:focusable="false"
                        android:clickable="true" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_date_to"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:hint="Hasta">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_date_to"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="date"
                        android:focusable="false"
                        android:clickable="true" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_search_guides"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Buscar Guías Disponibles" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Available Guides List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_available_guides"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/card_filters"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/layout_empty_guides"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@id/rv_available_guides"
        app:layout_constraintBottom_toBottomOf="@id/rv_available_guides"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:src="@android:drawable/ic_menu_myplaces"
            android:tint="@color/gray" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No hay guías disponibles"
            android:textSize="16sp"
            android:textColor="@color/gray"
            android:layout_marginTop="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Ajusta los filtros de búsqueda"
            android:textSize="14sp"
            android:textColor="@color/gray"
            android:layout_marginTop="8dp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
