<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Seguimiento de Guías"
        app:titleTextColor="@color/white"
        app:navigationIcon="@android:drawable/ic_menu_revert"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Map Container -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_map"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toTopOf="@id/card_active_guides"
        app:layout_constraintHeight_percent="0.6">

        <!-- Map Placeholder -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@color/light_gray">

            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:src="@android:drawable/ic_menu_mapmode"
                android:tint="@color/primary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Mapa en Tiempo Real"
                android:textSize="16sp"
                android:textColor="@color/primary"
                android:textStyle="bold"
                android:layout_marginTop="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ubicación de guías activos"
                android:textSize="14sp"
                android:textColor="@color/gray"
                android:layout_marginTop="8dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Active Guides List -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_active_guides"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.3">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!-- Header -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="16dp"
                android:background="@color/primary">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_myplaces"
                    android:tint="@color/white" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Guías Activos"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:layout_marginStart="12dp" />

                <TextView
                    android:id="@+id/tv_active_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="3 activos"
                    android:textSize="14sp"
                    android:textColor="@color/white" />

            </LinearLayout>

            <!-- Guides List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_active_guides"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:padding="8dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Filter FAB -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_filter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:src="@android:drawable/ic_menu_search"
        android:contentDescription="Filtrar Guías"
        app:layout_constraintBottom_toTopOf="@id/card_active_guides"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
