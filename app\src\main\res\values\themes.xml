<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme with Material Design 3 -->
    <style name="Base.Theme.DroidTour" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>

        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>

        <!-- Tertiary brand color -->
        <item name="colorTertiary">@color/md_theme_light_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_light_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_light_onTertiaryContainer</item>

        <!-- Error colors -->
        <item name="colorError">@color/md_theme_light_error</item>
        <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
        <item name="colorOnError">@color/md_theme_light_onError</item>
        <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>

        <!-- Background colors -->
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>

        <!-- Outline colors -->
        <item name="colorOutline">@color/md_theme_light_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_light_outlineVariant</item>

        <!-- Surface colors -->
        <item name="colorSurfaceTint">@color/md_theme_light_surfaceTint</item>
        <item name="colorSurfaceInverse">@color/md_theme_light_inverseSurface</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_light_inverseOnSurface</item>
        <item name="colorPrimaryInverse">@color/md_theme_light_inversePrimary</item>

        <!-- Typography -->
        <item name="textAppearanceDisplayLarge">@style/TextAppearance.DroidTour.DisplayLarge</item>
        <item name="textAppearanceHeadlineLarge">@style/TextAppearance.DroidTour.HeadlineLarge</item>
        <item name="textAppearanceTitleLarge">@style/TextAppearance.DroidTour.TitleLarge</item>
        <item name="textAppearanceBodyLarge">@style/TextAppearance.DroidTour.BodyLarge</item>
        <item name="textAppearanceLabelLarge">@style/TextAppearance.DroidTour.LabelLarge</item>
    </style>

    <style name="Theme.DroidTour" parent="Base.Theme.DroidTour" />

    <!-- Typography Styles -->
    <style name="TextAppearance.DroidTour.DisplayLarge" parent="TextAppearance.Material3.DisplayLarge">
        <item name="fontFamily">@font/roboto</item>
        <item name="android:fontWeight">400</item>
    </style>

    <style name="TextAppearance.DroidTour.HeadlineLarge" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="fontFamily">@font/roboto</item>
        <item name="android:fontWeight">400</item>
    </style>

    <style name="TextAppearance.DroidTour.TitleLarge" parent="TextAppearance.Material3.TitleLarge">
        <item name="fontFamily">@font/roboto</item>
        <item name="android:fontWeight">500</item>
    </style>

    <style name="TextAppearance.DroidTour.BodyLarge" parent="TextAppearance.Material3.BodyLarge">
        <item name="fontFamily">@font/roboto</item>
        <item name="android:fontWeight">400</item>
    </style>

    <style name="TextAppearance.DroidTour.LabelLarge" parent="TextAppearance.Material3.LabelLarge">
        <item name="fontFamily">@font/roboto</item>
        <item name="android:fontWeight">500</item>
    </style>

    <!-- Custom Card Style -->
    <style name="Widget.DroidTour.CardView" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">2dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="contentPadding">16dp</item>
    </style>

    <!-- Custom Button Styles -->
    <style name="Widget.DroidTour.Button" parent="Widget.Material3.Button">
        <item name="cornerRadius">12dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontWeight">500</item>
    </style>

    <style name="Widget.DroidTour.Button.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="cornerRadius">12dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontWeight">500</item>
    </style>
</resources>