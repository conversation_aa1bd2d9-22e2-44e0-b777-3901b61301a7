<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Ofertas de Tours"
        app:titleTextColor="@color/white"
        app:navigationIcon="@android:drawable/ic_menu_revert"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Search -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_search"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="12dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="2dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Buscar tour o empresa"
            app:startIconDrawable="@android:drawable/ic_menu_search"
            android:padding="8dp">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_search"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:imeOptions="actionSearch" />

        </com.google.android.material.textfield.TextInputLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Filter Section -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_filters"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="12dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="2dp"
        app:layout_constraintTop_toBottomOf="@id/card_search">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@android:drawable/ic_menu_sort_by_size"
                    android:tint="@color/primary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Filtrar"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/chip_group_filter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:singleSelection="true"
                app:singleLine="false"
                app:chipSpacingHorizontal="8dp"
                app:chipSpacingVertical="8dp">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_all"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Todas"
                    android:checkable="true"
                    android:checked="true"
                    app:checkedIconVisible="false" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_pending"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Pendientes"
                    android:checkable="true"
                    app:checkedIconVisible="false" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_high_pay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Mejor Pago"
                    android:checkable="true"
                    app:checkedIconVisible="false" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_today"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Hoy"
                    android:checkable="true"
                    app:checkedIconVisible="false" />

            </com.google.android.material.chip.ChipGroup>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Tour Offers List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_tour_offers"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:paddingBottom="88dp"
        android:clipToPadding="false"
        app:layout_constraintTop_toBottomOf="@id/card_filters"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/layout_empty_offers"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@id/rv_tour_offers"
        app:layout_constraintBottom_toBottomOf="@id/rv_tour_offers"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:src="@android:drawable/ic_menu_agenda"
            android:tint="@color/gray" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No hay ofertas disponibles"
            android:textSize="16sp"
            android:textColor="@color/gray"
            android:layout_marginTop="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Las empresas te enviarán propuestas aquí"
            android:textSize="14sp"
            android:textColor="@color/gray"
            android:layout_marginTop="8dp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
