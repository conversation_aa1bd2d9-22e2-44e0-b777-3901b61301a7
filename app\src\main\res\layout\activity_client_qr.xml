<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Mis QRs"
            app:titleTextColor="@color/white"
            app:navigationIcon="@android:drawable/ic_menu_revert" />

        <!-- QR Inicio -->

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="QR de Inicio"
                    android:textStyle="bold"
                    android:textSize="18sp"
                    android:textColor="@color/black"
                    android:layout_marginBottom="12dp" />

                <ImageView
                    android:layout_width="220dp"
                    android:layout_height="220dp"
                    android:layout_gravity="center_horizontal"
                    android:src="@android:drawable/ic_menu_share"
                    android:background="@color/white"
                    android:padding="24dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Muestra este código al guía al iniciar el tour"
                    android:textSize="14sp"
                    android:textColor="@color/gray"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="8dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- QR Fin -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="QR de Fin"
                    android:textStyle="bold"
                    android:textSize="18sp"
                    android:textColor="@color/black"
                    android:layout_marginBottom="12dp" />

                <ImageView
                    android:layout_width="220dp"
                    android:layout_height="220dp"
                    android:layout_gravity="center_horizontal"
                    android:src="@android:drawable/ic_menu_share"
                    android:background="@color/white"
                    android:padding="24dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Muestra este código al finalizar para tu check-out"
                    android:textSize="14sp"
                    android:textColor="@color/gray"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="8dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_rate_after_checkout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Valorar tour"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_marginTop="12dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>

