<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/light_gray">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Seleccionar Tipo de Usuario"
        app:titleTextColor="@color/white"
        app:navigationIcon="@android:drawable/ic_menu_revert" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="¿Cómo deseas registrarte?"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Selecciona el tipo de usuario que mejor describe tu rol en DroidTour"
                android:textSize="14sp"
                android:textColor="@color/gray"
                android:layout_marginBottom="32dp" />

            <!-- Cliente -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_register_client"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@android:drawable/ic_menu_myplaces"
                        android:layout_marginEnd="16dp"
                        app:tint="@color/primary" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Cliente"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/black" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Reserva tours, explora destinos y disfruta de experiencias únicas"
                            android:textSize="14sp"
                            android:textColor="@color/gray"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@android:drawable/ic_menu_send"
                        app:tint="@color/gray" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Guía de Turismo -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_register_guide"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@android:drawable/ic_menu_compass"
                        android:layout_marginEnd="16dp"
                        app:tint="@color/green" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Guía de Turismo"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/black" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Guía tours, comparte tu conocimiento y gana dinero"
                            android:textSize="14sp"
                            android:textColor="@color/gray"
                            android:layout_marginTop="4dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="*Requiere aprobación del Superadmin"
                            android:textSize="12sp"
                            android:textColor="@color/orange"
                            android:textStyle="italic"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@android:drawable/ic_menu_send"
                        app:tint="@color/gray" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Administrador de Empresa -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_register_admin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@android:drawable/ic_menu_manage"
                        android:layout_marginEnd="16dp"
                        app:tint="@color/orange" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Administrador de Empresa"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/black" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Gestiona tu empresa de turismo, crea tours y administra reservas"
                            android:textSize="14sp"
                            android:textColor="@color/gray"
                            android:layout_marginTop="4dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="*Requiere aprobación del Superadmin"
                            android:textSize="12sp"
                            android:textColor="@color/orange"
                            android:textStyle="italic"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@android:drawable/ic_menu_send"
                        app:tint="@color/gray" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
