<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Reportes de Ventas"
        app:titleTextColor="@color/white"
        app:navigationIcon="@android:drawable/ic_menu_revert"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Date Range Filter -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_date_filter"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Período:"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/primary" />

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/chip_group_period"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="16dp"
                app:singleSelection="true">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_daily"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Diario"
                    android:checkable="true"
                    android:checked="true" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_monthly"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Mensual"
                    android:checkable="true" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_annual"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Anual"
                    android:checkable="true" />

            </com.google.android.material.chip.ChipGroup>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Tab Layout -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:tabSelectedTextColor="@color/primary"
        app:tabTextColor="@color/gray"
        app:tabIndicatorColor="@color/primary"
        app:layout_constraintTop_toBottomOf="@id/card_date_filter">

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Por Servicio" />

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Por Tour" />

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Resumen" />

    </com.google.android.material.tabs.TabLayout>

    <!-- ViewPager Content -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/view_pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/tab_layout"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
