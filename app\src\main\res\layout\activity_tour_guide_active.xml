<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Tour en Progreso"
        app:titleTextColor="@color/white"
        app:navigationIcon="@android:drawable/ic_menu_revert"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Tour Info Card -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_tour_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:id="@+id/tv_tour_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Tour Machu Picchu"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/tv_tour_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="En progreso - Punto 2 de 5"
                android:textSize="14sp"
                android:textColor="@color/primary"
                android:layout_marginTop="4dp" />

            <TextView
                android:id="@+id/tv_current_location"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ubicación actual: Sacsayhuamán"
                android:textSize="14sp"
                android:textColor="@color/gray"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Map Container -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_map"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintTop_toBottomOf="@id/card_tour_info"
        app:layout_constraintBottom_toTopOf="@id/layout_action_buttons"
        app:layout_constraintHeight_percent="0.5">

        <!-- Placeholder for Map -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@color/light_gray">

            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@android:drawable/ic_menu_mapmode"
                android:tint="@color/primary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Mapa del Tour"
                android:textSize="16sp"
                android:textColor="@color/gray"
                android:layout_marginTop="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Tu ubicación y puntos del tour"
                android:textSize="12sp"
                android:textColor="@color/gray"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Action Buttons -->
    <LinearLayout
        android:id="@+id/layout_action_buttons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- Check-in/Check-out Buttons -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_register_location"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="Registrar Llegada a Punto"
            android:textSize="16sp"
            android:drawableStart="@android:drawable/ic_menu_mylocation"
            app:cornerRadius="8dp"
            android:layout_marginBottom="8dp" />

        <!-- QR Scanner Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:weightSum="2">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_scan_checkin"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="@string/check_in"
                android:textSize="14sp"
                android:drawableTop="@android:drawable/ic_menu_camera"
                app:cornerRadius="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_scan_checkout"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="@string/check_out"
                android:textSize="14sp"
                android:drawableTop="@android:drawable/ic_menu_camera"
                app:cornerRadius="8dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
