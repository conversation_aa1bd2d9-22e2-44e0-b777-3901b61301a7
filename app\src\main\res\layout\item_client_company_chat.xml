<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Avatar/Icono de empresa -->
        <ImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@android:drawable/ic_menu_myplaces"
            android:layout_marginEnd="16dp"
            app:tint="@color/primary"
            android:background="@drawable/circle_light_gray" />

        <!-- Contenido del chat -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Header con nombre y timestamp -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <TextView
                    android:id="@+id/tv_company_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Tours Cusco Adventures"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:textColor="@color/black" />

                <TextView
                    android:id="@+id/tv_timestamp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2:30 PM"
                    android:textSize="12sp"
                    android:textColor="@color/gray" />

            </LinearLayout>

            <!-- Último mensaje -->
            <TextView
                android:id="@+id/tv_last_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Perfecto, nos vemos mañana a las 8 AM"
                android:textSize="14sp"
                android:textColor="@color/gray"
                android:maxLines="2"
                android:ellipsize="end" />

        </LinearLayout>

        <!-- Indicador de mensajes no leídos -->
        <TextView
            android:id="@+id/tv_unread_count"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:text="2"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:background="@drawable/circle_primary"
            android:gravity="center"
            android:layout_marginStart="8dp"
            android:visibility="gone" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
