<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Escanear QR"
        app:titleTextColor="@color/white"
        app:navigationIcon="@android:drawable/ic_menu_revert"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Camera Preview Placeholder -->
    <LinearLayout
        android:id="@+id/layout_camera_preview"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:gravity="center"
        android:background="@color/black"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toTopOf="@id/card_scan_info">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📷"
            android:textSize="64sp"
            android:textColor="@color/white"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Vista de Cámara"
            android:textSize="18sp"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Apunta la cámara al código QR"
            android:textSize="14sp"
            android:textColor="@color/white"
            android:layout_marginTop="8dp" />

        <!-- QR Frame Overlay -->
        <LinearLayout
            android:layout_width="250dp"
            android:layout_height="250dp"
            android:layout_marginTop="32dp"
            android:background="@drawable/qr_frame_overlay"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Coloca el QR aquí"
                android:textSize="14sp"
                android:textColor="@color/white"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

    <!-- Scan Information Card -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_scan_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintBottom_toTopOf="@id/layout_scan_actions">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Información del Tour"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:layout_marginBottom="12dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tour: "
                    android:textSize="14sp"
                    android:textColor="@color/gray"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_tour_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="City Tour Lima Centro"
                    android:textSize="14sp"
                    android:textColor="@color/black" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Participantes: "
                    android:textSize="14sp"
                    android:textColor="@color/gray"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_participants_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="6 personas"
                    android:textSize="14sp"
                    android:textColor="@color/black" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Estado: "
                    android:textSize="14sp"
                    android:textColor="@color/gray"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_scan_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Esperando QR de Check-in"
                    android:textSize="14sp"
                    android:textColor="@color/orange" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Scan Actions -->
    <LinearLayout
        android:id="@+id/layout_scan_actions"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_toggle_flash"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Flash"
            android:drawableStart="@android:drawable/ic_menu_help"
            android:layout_marginEnd="16dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_manual_entry"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Entrada Manual"
            android:textColor="@color/white" />

    </LinearLayout>

    <!-- Success/Error Overlay -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_scan_result"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="32dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="8dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp"
            android:gravity="center">

            <ImageView
                android:id="@+id/iv_scan_result_icon"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:src="@android:drawable/ic_menu_agenda"
                android:tint="@color/green" />

            <TextView
                android:id="@+id/tv_scan_result_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="¡Check-in Exitoso!"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/green"
                android:layout_marginTop="16dp" />

            <TextView
                android:id="@+id/tv_scan_result_message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cliente registrado correctamente"
                android:textSize="16sp"
                android:textColor="@color/black"
                android:gravity="center"
                android:layout_marginTop="8dp" />

            <TextView
                android:id="@+id/tv_client_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ana García Pérez"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:layout_marginTop="12dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_continue_scanning"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Continuar"
                android:layout_marginTop="16dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</androidx.constraintlayout.widget.ConstraintLayout>
