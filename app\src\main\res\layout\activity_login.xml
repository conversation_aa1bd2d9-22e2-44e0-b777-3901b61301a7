<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@android:style/Widget.DeviceDefault.Light.ScrollView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray"
    android:id="@+id/main" >

    <ImageView
        android:id="@+id/imageninit"
        android:layout_width="421dp"
        android:layout_height="483dp"
        android:src="@drawable/head"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="-100dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.497"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mensaje1"
        app:layout_constraintVertical_bias="0.091" />

    <ScrollView
        style="@android:style/Widget.DeviceDefault.Light.ScrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="24dp">


            <!-- Logo/Title -->

            <!-- Login Form -->

            <TextView
                android:id="@+id/tv_app_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/bricolage_grotesque_medium"
                android:text="@string/app_name"
                android:textAppearance="@style/droidtour.white"
                app:layout_constraintBottom_toTopOf="@+id/card_login"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.493"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_login"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:layout_editor_absoluteY="264dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/email"
                        app:startIconDrawable="@android:drawable/ic_dialog_email">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_email"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textEmailAddress" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/password"
                        app:endIconMode="password_toggle"
                        app:startIconDrawable="@android:drawable/ic_lock_lock">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_password"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPassword" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <TextView
                        android:id="@+id/tv_forgot_password"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="8dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:text="@string/forgot_password"
                        android:textColor="@color/primary"
                        android:textSize="14sp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_login"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:layout_marginTop="24dp"

                        android:text="@string/login"
                        android:textSize="16sp"
                        app:cornerRadius="8dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Register Section -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/card_login">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="¿No tienes cuenta? "
                    android:textColor="@color/dark_gray" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_register"
                    style="@style/Widget.Material3.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/register" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>


    </ScrollView>
</FrameLayout>
