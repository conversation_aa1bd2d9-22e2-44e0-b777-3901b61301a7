<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Main Content -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="@string/superadmin"
            app:titleTextColor="@color/white"
            app:navigationIcon="@android:drawable/ic_menu_sort_alphabetically"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Dashboard Cards -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:padding="16dp"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            app:layout_constraintBottom_toBottomOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- User Management Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_user_management"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@android:drawable/ic_menu_manage"
                            android:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/user_management"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Gestionar usuarios del sistema"
                                android:textSize="14sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_more"
                            android:tint="@color/gray" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Reports Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_reports"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@android:drawable/ic_menu_report_image"
                            android:tint="@color/green" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/tour_reports"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Ver reportes de reservas"
                                android:textSize="14sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_more"
                            android:tint="@color/gray" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- System Logs Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_logs"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@android:drawable/ic_menu_info_details"
                            android:tint="@color/orange" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/system_logs"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Ver logs del sistema"
                                android:textSize="14sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_more"
                            android:tint="@color/gray" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </ScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        app:menu="@menu/superadmin_nav_menu" />

</androidx.drawerlayout.widget.DrawerLayout>
