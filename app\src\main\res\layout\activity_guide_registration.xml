<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Registro de Guía"
            app:titleTextColor="@color/white"
            app:navigationIcon="@android:drawable/ic_menu_revert"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Registration Form -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_registration_form"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:layout_constraintTop_toBottomOf="@id/toolbar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <!-- Photo Section -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Foto de Perfil"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp" />

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_profile_photo"
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginBottom="24dp"
                    app:cardCornerRadius="60dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:background="@color/light_gray">

                        <ImageView
                            android:id="@+id/iv_profile_photo"
                            android:layout_width="60dp"
                            android:layout_height="60dp"
                            android:src="@android:drawable/ic_menu_camera"
                            android:tint="@color/gray" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Agregar Foto"
                            android:textSize="12sp"
                            android:textColor="@color/gray"
                            android:layout_marginTop="8dp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Personal Info Section -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Información Personal"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_first_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:hint="Nombres">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_first_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPersonName" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_last_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:hint="Apellidos">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_last_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPersonName" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

                <!-- Document Info -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_document_type"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:hint="Tipo de Documento"
                        app:endIconMode="dropdown_menu">

                        <AutoCompleteTextView
                            android:id="@+id/act_document_type"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_document_number"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:hint="Número de Documento">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_document_number"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_birth_date"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Fecha de Nacimiento"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_birth_date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="date"
                        android:focusable="false"
                        android:clickable="true" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Contact Info Section -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Información de Contacto"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="16dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Correo Electrónico"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textEmailAddress" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Teléfono"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_phone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="phone" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Domicilio"
                    android:layout_marginBottom="24dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_address"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPostalAddress"
                        android:lines="2" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Languages Section -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Idiomas que Habla"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp" />

                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/chip_group_languages"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_spanish"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Español"
                        android:checkable="true"
                        android:checked="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_english"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Inglés"
                        android:checkable="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_french"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Francés"
                        android:checkable="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_german"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Alemán"
                        android:checkable="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_italian"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Italiano"
                        android:checkable="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_chinese"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Chino"
                        android:checkable="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_japanese"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Japonés"
                        android:checkable="true" />

                </com.google.android.material.chip.ChipGroup>

                <!-- Terms and Conditions -->
                <CheckBox
                    android:id="@+id/cb_terms"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Acepto los términos y condiciones"
                    android:layout_marginBottom="24dp" />

                <!-- Action Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="end">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_cancel"
                        style="@style/Widget.Material3.Button.TextButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/cancel"
                        android:layout_marginEnd="16dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_register"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Registrarse" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
