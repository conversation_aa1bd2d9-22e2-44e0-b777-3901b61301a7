<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Tour Image -->
        <ImageView
            android:id="@+id/iv_tour_image"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:src="@android:drawable/ic_menu_mapmode"
            android:background="@color/light_gray"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Tour Info -->
        <TextView
            android:id="@+id/tv_tour_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Tour Machu Picchu"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginStart="12dp"
            app:layout_constraintStart_toEndOf="@id/iv_tour_image"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/iv_tour_image" />

        <TextView
            android:id="@+id/tv_company_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Empresa Tours Cusco"
            android:textSize="14sp"
            android:textColor="@color/primary"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            app:layout_constraintStart_toEndOf="@id/iv_tour_image"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_tour_name" />

        <TextView
            android:id="@+id/tv_tour_duration"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Duración: 8 horas"
            android:textSize="12sp"
            android:textColor="@color/gray"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            app:layout_constraintStart_toEndOf="@id/iv_tour_image"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_company_name" />

        <TextView
            android:id="@+id/tv_tour_dates"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="15 Ene - 20 Ene 2025"
            android:textSize="12sp"
            android:textColor="@color/gray"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            app:layout_constraintStart_toEndOf="@id/iv_tour_image"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_tour_duration" />

        <!-- Payment Offer -->
        <LinearLayout
            android:id="@+id/layout_payment_offer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@color/light_gray"
            android:padding="12dp"
            android:layout_marginTop="12dp"
            app:layout_constraintTop_toBottomOf="@id/iv_tour_image"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Pago Ofrecido:"
                android:textSize="14sp"
                android:textColor="@color/dark_gray" />

            <TextView
                android:id="@+id/tv_payment_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="S/. 150.00"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/green" />

            <!-- Precio genérico para reutilizar esta tarjeta con historial/reservas -->
            <TextView
                android:id="@+id/tv_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="S/. 150.00"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/green"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end"
            android:layout_marginTop="12dp"
            app:layout_constraintTop_toBottomOf="@id/layout_payment_offer"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_view_details"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ver Detalles"
                android:textSize="12sp"
                android:layout_marginEnd="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_decline_offer"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Rechazar"
                android:textSize="12sp"
                android:layout_marginEnd="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_accept_offer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Aceptar"
                android:textSize="12sp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
