<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="@string/tour_reports"
        app:titleTextColor="@color/white"
        app:navigationIcon="@android:drawable/ic_menu_revert"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="16dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Reports Summary Cards -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@android:drawable/ic_menu_report_image"
                        android:tint="@color/green" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Reportes de Reservas"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/black" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Ver estadísticas de reservas por empresa"
                            android:textSize="14sp"
                            android:textColor="@color/gray"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="1,234"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/green" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Charts Placeholder -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="300dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="24dp">

                    <ImageView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:src="@android:drawable/ic_menu_report_image"
                        android:tint="@color/primary"
                        android:alpha="0.7" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Gráficos de Reportes"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginTop="16dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Estadísticas de reservas por empresa de turismo"
                        android:textSize="14sp"
                        android:textColor="@color/gray"
                        android:gravity="center"
                        android:layout_marginTop="8dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Filter Options -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Filtros de Reporte"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginBottom="12dp" />

                    <com.google.android.material.chip.ChipGroup
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp">

                        <com.google.android.material.chip.Chip
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Diario"
                            android:checkable="true" />

                        <com.google.android.material.chip.Chip
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Mensual"
                            android:checkable="true"
                            android:checked="true" />

                        <com.google.android.material.chip.Chip
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Anual"
                            android:checkable="true" />

                    </com.google.android.material.chip.ChipGroup>

                    <com.google.android.material.button.MaterialButton
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Generar Reporte"
                        app:cornerRadius="8dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
