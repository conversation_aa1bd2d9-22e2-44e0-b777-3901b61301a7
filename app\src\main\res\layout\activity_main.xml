<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray"
    tools:context=".MainActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="24dp">

        <!-- App Logo/Title -->
        <TextView
            android:id="@+id/tv_app_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textSize="36sp"
            android:textStyle="bold"
            android:textColor="@color/primary"
            android:layout_marginTop="48dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/tv_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Sistema de Gestión de Tours"
            android:textSize="16sp"
            android:textColor="@color/gray"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/tv_app_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Role Selection Cards -->
        <TextView
            android:id="@+id/tv_demo_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Selecciona un Rol para Demo"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginTop="48dp"
            app:layout_constraintTop_toBottomOf="@id/tv_subtitle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Superadmin Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_superadmin"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:layout_marginTop="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintTop_toBottomOf="@id/tv_demo_title">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@android:drawable/ic_menu_manage"
                    android:tint="@color/primary" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/superadmin"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/black" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Gestión de usuarios y reportes del sistema"
                        android:textSize="14sp"
                        android:textColor="@color/gray" />

                </LinearLayout>

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_more"
                    android:tint="@color/gray" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Tour Admin Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_tour_admin"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintTop_toBottomOf="@id/card_superadmin">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@android:drawable/ic_menu_info_details"
                    android:tint="@color/green" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/tour_admin"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/black" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Administrador de empresa de turismo"
                        android:textSize="14sp"
                        android:textColor="@color/gray" />

                </LinearLayout>

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_more"
                    android:tint="@color/gray" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Tour Guide Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_tour_guide"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintTop_toBottomOf="@id/card_tour_admin">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@android:drawable/ic_menu_myplaces"
                    android:tint="@color/orange" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/tour_guide"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/black" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Guía de turismo profesional"
                        android:textSize="14sp"
                        android:textColor="@color/gray" />

                </LinearLayout>

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_more"
                    android:tint="@color/gray" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Client Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_client"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintTop_toBottomOf="@id/card_tour_guide">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@android:drawable/ic_menu_compass"
                    android:tint="@color/accent" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/client"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/black" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Cliente que reserva tours"
                        android:textSize="14sp"
                        android:textColor="@color/gray" />

                </LinearLayout>

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_more"
                    android:tint="@color/gray" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Login Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_login"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginTop="32dp"
            android:text="@string/login"
            android:textSize="16sp"
            app:cornerRadius="8dp"
            app:layout_constraintTop_toBottomOf="@id/card_client" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>