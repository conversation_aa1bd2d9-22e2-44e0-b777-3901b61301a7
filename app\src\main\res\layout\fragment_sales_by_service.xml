<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <!-- Chart Container -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_chart"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@color/light_gray">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@android:drawable/ic_menu_report_image"
                android:tint="@color/primary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Gráfico de Ventas por Servicio"
                android:textSize="14sp"
                android:textColor="@color/primary"
                android:textStyle="bold"
                android:layout_marginTop="12dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="(De menor a mayor)"
                android:textSize="12sp"
                android:textColor="@color/gray"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Services List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_services_sales"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/card_chart"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/layout_empty_services"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@id/rv_services_sales"
        app:layout_constraintBottom_toBottomOf="@id/rv_services_sales"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:src="@android:drawable/ic_menu_report_image"
            android:tint="@color/gray" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No hay datos de ventas"
            android:textSize="16sp"
            android:textColor="@color/gray"
            android:layout_marginTop="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Selecciona un período diferente"
            android:textSize="14sp"
            android:textColor="@color/gray"
            android:layout_marginTop="8dp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
