<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Main Content -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="@string/tour_guide"
            app:titleTextColor="@color/white"
            app:navigationIcon="@android:drawable/ic_menu_sort_alphabetically"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Dashboard Content -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:padding="16dp"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            app:layout_constraintBottom_toBottomOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Status Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_guide_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/green">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@android:drawable/ic_menu_myplaces"
                            android:tint="@color/white" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_guide_status"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Estado: APROBADO"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/white" />

                            <TextView
                                android:id="@+id/tv_guide_rating"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Calificación: ⭐ 4.8 (25 tours)"
                                android:textSize="14sp"
                                android:textColor="@color/white"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_earnings_today"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="S/. 150"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Tour Offers Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_tour_offers"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@android:drawable/ic_menu_agenda"
                            android:tint="@color/orange" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Ofertas de Tours"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Ver propuestas de empresas"
                                android:textSize="14sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_pending_offers_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="3"
                            android:textSize="14sp"
                            android:textColor="@color/white"
                            android:background="@drawable/circle_orange"
                            android:gravity="center"
                            android:minWidth="28dp"
                            android:minHeight="28dp"
                            android:layout_marginEnd="8dp" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_more"
                            android:tint="@color/gray" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- My Tours Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_my_tours"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@android:drawable/ic_menu_mapmode"
                            android:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Mis Tours"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Tours asignados y activos"
                                android:textSize="14sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_active_tours_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="2"
                            android:textSize="14sp"
                            android:textColor="@color/white"
                            android:background="@drawable/circle_primary"
                            android:gravity="center"
                            android:minWidth="28dp"
                            android:minHeight="28dp"
                            android:layout_marginEnd="8dp" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_more"
                            android:tint="@color/gray" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- QR Scanner Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_qr_scanner"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@android:drawable/ic_menu_camera"
                            android:tint="@color/accent" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Escanear QR"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Check-in y check-out de clientes"
                                android:textSize="14sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_more"
                            android:tint="@color/gray" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Location Tracking Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_location_tracking"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@android:drawable/ic_menu_mylocation"
                            android:tint="@color/green" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Registrar Ubicación"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Marcar puntos del tour"
                                android:textSize="14sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_more"
                            android:tint="@color/gray" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </ScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        app:menu="@menu/tour_guide_nav_menu" />

</androidx.drawerlayout.widget.DrawerLayout>
