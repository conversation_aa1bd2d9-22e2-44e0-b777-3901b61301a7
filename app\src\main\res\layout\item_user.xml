<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- User Avatar -->
        <ImageView
            android:id="@+id/iv_user_avatar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@android:drawable/ic_menu_myplaces"
            android:background="@color/light_gray"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- User Info -->
        <TextView
            android:id="@+id/tv_user_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Juan Pérez"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginStart="12dp"
            app:layout_constraintStart_toEndOf="@id/iv_user_avatar"
            app:layout_constraintEnd_toStartOf="@id/chip_user_type"
            app:layout_constraintTop_toTopOf="@id/iv_user_avatar" />

        <TextView
            android:id="@+id/tv_user_email"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="<EMAIL>"
            android:textSize="14sp"
            android:textColor="@color/gray"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            app:layout_constraintStart_toEndOf="@id/iv_user_avatar"
            app:layout_constraintEnd_toStartOf="@id/chip_user_type"
            app:layout_constraintTop_toBottomOf="@id/tv_user_name" />

        <!-- User Type Chip -->
        <com.google.android.material.chip.Chip
            android:id="@+id/chip_user_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/client"
            android:textSize="12sp"
            app:chipBackgroundColor="@color/primary"
            app:chipStrokeWidth="0dp"
            android:textColor="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Status Switch -->
        <com.google.android.material.switchmaterial.SwitchMaterial
            android:id="@+id/switch_user_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Activo"
            android:textSize="12sp"
            android:checked="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/chip_user_type"
            android:layout_marginTop="8dp" />

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="12dp"
            app:layout_constraintStart_toStartOf="@id/tv_user_name"
            app:layout_constraintTop_toBottomOf="@id/tv_user_email">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_view_user"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:text="@string/view"
                android:textSize="12sp"
                android:minWidth="0dp"
                android:paddingHorizontal="12dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_edit_user"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:text="@string/edit"
                android:textSize="12sp"
                android:minWidth="0dp"
                android:paddingHorizontal="12dp"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
