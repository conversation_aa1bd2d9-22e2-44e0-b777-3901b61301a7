<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Main Content -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="@string/client"
            app:titleTextColor="@color/white"
            app:navigationIcon="@android:drawable/ic_menu_sort_alphabetically"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Search Bar -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_search"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:hint="Buscar empresas o tours"
            app:startIconDrawable="@android:drawable/ic_menu_search"
            app:layout_constraintTop_toBottomOf="@id/toolbar">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_search"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Quick Actions -->
        <LinearLayout
            android:id="@+id/layout_quick_actions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="16dp"
            app:layout_constraintTop_toBottomOf="@id/til_search">

            <!-- Primera fila -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_my_reservations"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="@string/my_reservations"
                    android:textSize="12sp"
                    android:drawableTop="@android:drawable/ic_menu_agenda" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_tour_history"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="@string/tour_history"
                    android:textSize="12sp"
                    android:drawableTop="@android:drawable/ic_menu_recent_history" />

            </LinearLayout>

            <!-- Segunda fila -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_chat"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="Chat Soporte"
                    android:textSize="12sp"
                    android:drawableTop="@android:drawable/ic_menu_send" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_real_time_tracking"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="Seguimiento"
                    android:textSize="12sp"
                    android:drawableTop="@android:drawable/ic_menu_mylocation" />

            </LinearLayout>

        </LinearLayout>

        <!-- Companies List -->
        <TextView
            android:id="@+id/tv_companies_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/available_companies"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginStart="16dp"
            android:layout_marginBottom="8dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_quick_actions" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_companies"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:padding="16dp"
            app:layout_constraintTop_toBottomOf="@id/tv_companies_title"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!-- Empty State -->
        <LinearLayout
            android:id="@+id/layout_empty_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:src="@android:drawable/ic_menu_mapmode"
                android:tint="@color/gray"
                android:alpha="0.5" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="No hay empresas disponibles"
                android:textSize="18sp"
                android:textColor="@color/gray"
                android:layout_marginTop="16dp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        app:menu="@menu/client_nav_menu" />

</androidx.drawerlayout.widget.DrawerLayout>
