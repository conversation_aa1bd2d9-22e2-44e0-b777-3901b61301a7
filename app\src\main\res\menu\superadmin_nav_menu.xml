<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android">

    <group android:checkableBehavior="single">
        
        <item
            android:id="@+id/nav_home"
            android:icon="@android:drawable/ic_menu_preferences"
            android:title="@string/home" />

        <item
            android:id="@+id/nav_user_management"
            android:icon="@android:drawable/ic_menu_manage"
            android:title="@string/user_management" />

        <item
            android:id="@+id/nav_reports"
            android:icon="@android:drawable/ic_menu_report_image"
            android:title="@string/tour_reports" />

        <item
            android:id="@+id/nav_logs"
            android:icon="@android:drawable/ic_menu_info_details"
            android:title="@string/system_logs" />

    </group>

    <item android:title="Cuenta">
        <menu>
            <item
                android:id="@+id/nav_profile"
                android:icon="@android:drawable/ic_menu_myplaces"
                android:title="@string/profile" />
            
            <item
                android:id="@+id/nav_logout"
                android:icon="@android:drawable/ic_lock_power_off"
                android:title="@string/logout" />
        </menu>
    </item>

</menu>
