<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <!-- Pending Proposals Section -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_pending_proposals"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_agenda"
                    android:tint="@color/orange" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Propuestas Pendientes"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginStart="12dp" />

                <TextView
                    android:id="@+id/tv_pending_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="3"
                    android:textSize="14sp"
                    android:textColor="@color/white"
                    android:background="@drawable/circle_orange"
                    android:gravity="center"
                    android:minWidth="24dp"
                    android:minHeight="24dp" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_pending_proposals"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Accepted Proposals Section -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_accepted_proposals"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintTop_toBottomOf="@id/card_pending_proposals">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_agenda"
                    android:tint="@color/green" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Propuestas Aceptadas"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginStart="12dp" />

                <TextView
                    android:id="@+id/tv_accepted_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="5"
                    android:textSize="14sp"
                    android:textColor="@color/white"
                    android:background="@drawable/circle_green"
                    android:gravity="center"
                    android:minWidth="24dp"
                    android:minHeight="24dp" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_accepted_proposals"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Rejected Proposals Section -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_rejected_proposals"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:layout_constraintTop_toBottomOf="@id/card_accepted_proposals"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_agenda"
                    android:tint="@color/red" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Propuestas Rechazadas"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginStart="12dp" />

                <TextView
                    android:id="@+id/tv_rejected_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2"
                    android:textSize="14sp"
                    android:textColor="@color/white"
                    android:background="@drawable/circle_red"
                    android:gravity="center"
                    android:minWidth="24dp"
                    android:minHeight="24dp" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_rejected_proposals"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:nestedScrollingEnabled="false" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- FAB for New Proposal -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_new_proposal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:src="@android:drawable/ic_input_add"
        android:contentDescription="Nueva Propuesta"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
