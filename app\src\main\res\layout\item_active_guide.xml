<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <!-- Guide Status Indicator -->
        <View
            android:id="@+id/view_status_indicator"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:background="@drawable/circle_green"
            android:layout_marginEnd="12dp" />

        <!-- Guide Photo -->
        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_guide_photo"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@android:drawable/ic_menu_myplaces"
            android:scaleType="centerCrop"
            app:shapeAppearanceOverlay="@style/CircleImageView"
            android:background="@color/light_gray" />

        <!-- Guide Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="12dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_guide_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Carlos Mendoza"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/tv_tour_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="City Tour Lima Centro"
                android:textSize="12sp"
                android:textColor="@color/gray"
                android:layout_marginTop="2dp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="4dp">

                <ImageView
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:src="@android:drawable/ic_menu_mylocation"
                    android:tint="@color/primary" />

                <TextView
                    android:id="@+id/tv_current_location"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Plaza de Armas"
                    android:textSize="11sp"
                    android:textColor="@color/primary"
                    android:layout_marginStart="4dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Time and Actions -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal">

            <TextView
                android:id="@+id/tv_last_update"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Hace 2 min"
                android:textSize="11sp"
                android:textColor="@color/gray" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="4dp">

                <ImageView
                    android:id="@+id/iv_locate_guide"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_mylocation"
                    android:tint="@color/primary"
                    android:background="?android:attr/selectableItemBackgroundBorderless"
                    android:padding="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:contentDescription="Localizar en mapa" />

                <ImageView
                    android:id="@+id/iv_contact_guide"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_call"
                    android:tint="@color/green"
                    android:background="?android:attr/selectableItemBackgroundBorderless"
                    android:padding="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:contentDescription="Contactar guía"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
