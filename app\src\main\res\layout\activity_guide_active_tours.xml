<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Mis Tours"
        app:titleTextColor="@color/white"
        app:navigationIcon="@android:drawable/ic_menu_revert"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Tab Layout -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:tabSelectedTextColor="@color/primary"
        app:tabTextColor="@color/gray"
        app:tabIndicatorColor="@color/primary"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Hoy" />

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Próximos" />

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Historial" />

    </com.google.android.material.tabs.TabLayout>

    <!-- Summary Card -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_summary"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="12dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="2dp"
        app:layout_constraintTop_toBottomOf="@id/tab_layout">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@android:drawable/ic_menu_today"
                android:tint="@color/primary" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="12dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_today_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tours de hoy: 2"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:textColor="@color/black" />

                <TextView
                    android:id="@+id/tv_next_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Próximos: 3 • Historial: 12"
                    android:textSize="14sp"
                    android:textColor="@color/gray"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_earnings_today"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="S/. 150"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/green" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Tours List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_my_tours"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:clipToPadding="false"
        app:layout_constraintTop_toBottomOf="@id/card_summary"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
