<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Key Metrics Cards -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@android:drawable/ic_menu_help"
                        android:tint="@color/green" />

                    <TextView
                        android:id="@+id/tv_total_revenue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/. 12,450"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/green"
                        android:layout_marginTop="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Ingresos Totales"
                        android:textSize="12sp"
                        android:textColor="@color/gray"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@android:drawable/ic_menu_mapmode"
                        android:tint="@color/primary" />

                    <TextView
                        android:id="@+id/tv_total_tours"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="47"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginTop="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Tours Vendidos"
                        android:textSize="12sp"
                        android:textColor="@color/gray"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="24dp">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@android:drawable/ic_menu_myplaces"
                        android:tint="@color/orange" />

                    <TextView
                        android:id="@+id/tv_avg_ticket"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/. 265"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/orange"
                        android:layout_marginTop="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Ticket Promedio"
                        android:textSize="12sp"
                        android:textColor="@color/gray"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@android:drawable/btn_star_big_on"
                        android:tint="@color/accent" />

                    <TextView
                        android:id="@+id/tv_avg_rating"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="4.6"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/accent"
                        android:layout_marginTop="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Calificación Prom."
                        android:textSize="12sp"
                        android:textColor="@color/gray"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

        <!-- Top Performing Tours -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tours Más Vendidos"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_top_tours"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Monthly Trend Chart -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="250dp"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tendencia Mensual"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:background="@color/light_gray">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@android:drawable/ic_menu_report_image"
                        android:tint="@color/primary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Gráfico de Tendencia"
                        android:textSize="14sp"
                        android:textColor="@color/primary"
                        android:layout_marginTop="12dp" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Financial Breakdown -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Desglose Financiero"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Ingresos Brutos:"
                        android:textSize="16sp"
                        android:textColor="@color/black" />

                    <TextView
                        android:id="@+id/tv_gross_revenue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/. 12,450.00"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/green" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Comisión Plataforma (10%):"
                        android:textSize="16sp"
                        android:textColor="@color/black" />

                    <TextView
                        android:id="@+id/tv_platform_fee"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/. 1,245.00"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/red" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Pagos a Guías:"
                        android:textSize="16sp"
                        android:textColor="@color/black" />

                    <TextView
                        android:id="@+id/tv_guide_payments"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/. 3,500.00"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/orange" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray"
                    android:layout_marginVertical="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Ingresos Netos:"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/black" />

                    <TextView
                        android:id="@+id/tv_net_revenue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/. 7,705.00"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/green" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
