<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FE003B95">

    <ImageView
        android:id="@+id/imageninit"
        android:layout_width="242dp"
        android:layout_height="361dp"
        android:src="@drawable/saludo_init"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.497"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mensaje1"
        app:layout_constraintVertical_bias="0.091" />

    <TextView
        android:id="@+id/saltar"
        android:layout_width="93dp"
        android:layout_height="40dp"
        android:layout_alignParentTop="true"
        android:fontFamily="@font/bricolage_grotesque"
        android:text="@string/saltar"
        android:textAlignment="center"
        android:textAllCaps="false"
        android:textColor="#FFFFFF"
        android:textSize="21sp"
        app:layout_constraintBottom_toTopOf="@+id/mensaje1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.949"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/mensaje1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/bricolage_grotesque"
        android:text="@string/mensaje1"
        android:textColor="#FFFFFF"
        android:textSize="27sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.495"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.096" />

    <TextView
        android:id="@+id/mensaje2"
        android:layout_width="345dp"
        android:layout_height="71dp"
        android:fontFamily="@font/bricolage_grotesque"
        android:text="@string/mensaje2"
        android:textAlignment="center"
        android:textColor="#FFFFFF"
        android:textSize="25sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imageninit"
        app:layout_constraintVertical_bias="0.184" />

</androidx.constraintlayout.widget.ConstraintLayout>