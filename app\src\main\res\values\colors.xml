<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Material Design 3 Color System -->

    <!-- Primary Colors -->
    <color name="md_theme_light_primary">#1565C0</color>
    <color name="md_theme_light_onPrimary">#FFFFFF</color>
    <color name="md_theme_light_primaryContainer">#D3E4FD</color>
    <color name="md_theme_light_onPrimaryContainer">#001C38</color>

    <!-- Secondary Colors -->
    <color name="md_theme_light_secondary">#535F70</color>
    <color name="md_theme_light_onSecondary">#FFFFFF</color>
    <color name="md_theme_light_secondaryContainer">#D7E3F7</color>
    <color name="md_theme_light_onSecondaryContainer">#101C2B</color>

    <!-- Tertiary Colors -->
    <color name="md_theme_light_tertiary">#6B5778</color>
    <color name="md_theme_light_onTertiary">#FFFFFF</color>
    <color name="md_theme_light_tertiaryContainer">#F2DAFF</color>
    <color name="md_theme_light_onTertiaryContainer">#251431</color>

    <!-- Error Colors -->
    <color name="md_theme_light_error">#BA1A1A</color>
    <color name="md_theme_light_errorContainer">#FFDAD6</color>
    <color name="md_theme_light_onError">#FFFFFF</color>
    <color name="md_theme_light_onErrorContainer">#410002</color>

    <!-- Background Colors -->
    <color name="md_theme_light_background">#FDFCFF</color>
    <color name="md_theme_light_onBackground">#1A1C1E</color>
    <color name="md_theme_light_surface">#FDFCFF</color>
    <color name="md_theme_light_onSurface">#1A1C1E</color>
    <color name="md_theme_light_surfaceVariant">#DFE2EB</color>
    <color name="md_theme_light_onSurfaceVariant">#43474E</color>

    <!-- Outline Colors -->
    <color name="md_theme_light_outline">#73777F</color>
    <color name="md_theme_light_outlineVariant">#C3C7CF</color>

    <!-- Surface Colors -->
    <color name="md_theme_light_surfaceTint">#1565C0</color>
    <color name="md_theme_light_inverseSurface">#2F3033</color>
    <color name="md_theme_light_inverseOnSurface">#F1F0F4</color>
    <color name="md_theme_light_inversePrimary">#A4C8FF</color>

    <!-- Legacy colors for backward compatibility -->
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    <color name="primary">@color/md_theme_light_primary</color>
    <color name="primary_dark">#0D47A1</color>
    <color name="accent">#FF4081</color>
    <color name="gray">#9E9E9E</color>
    <color name="light_gray">#F5F5F5</color>
    <color name="dark_gray">#616161</color>
    <color name="green">#4CAF50</color>
    <color name="red">#F44336</color>
    <color name="orange">#FF9800</color>
    <color name="blue">#2196F3</color>

    <!-- Semantic colors for UI components -->
    <color name="surface_container_low">#F7F2FA</color>
    <color name="surface_container">#F1ECF4</color>
    <color name="surface_container_high">#ECE6F0</color>
    <color name="surface_container_highest">#E6E0E9</color>
</resources>