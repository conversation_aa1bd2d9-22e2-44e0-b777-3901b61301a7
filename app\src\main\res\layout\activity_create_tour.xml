<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="@string/create_tour"
            app:titleTextColor="@color/white"
            app:navigationIcon="@android:drawable/ic_menu_revert"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Tour Form -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_tour_form"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:layout_constraintTop_toBottomOf="@id/toolbar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <!-- Basic Info Section -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Información Básica"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_tour_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Nombre del Tour"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_tour_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_tour_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Descripción"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_tour_description"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:lines="3" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_tour_price"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Precio por persona"
                    android:layout_marginBottom="16dp"
                    app:prefixText="S/. ">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_tour_price"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="numberDecimal" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_tour_duration"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Duración (horas)"
                    android:layout_marginBottom="24dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_tour_duration"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Dates Section -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Fechas"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="24dp">

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_start_date"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:hint="Fecha de inicio">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_start_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="date"
                            android:focusable="false"
                            android:clickable="true" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_end_date"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:hint="Fecha de fin">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_end_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="date"
                            android:focusable="false"
                            android:clickable="true" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

                <!-- Locations Section -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Ubicaciones del Tour"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_add_location"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Agregar Ubicación en Mapa"
                    android:drawableStart="@android:drawable/ic_menu_mapmode"
                    android:layout_marginBottom="16dp" />

                <!-- Locations List -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_locations"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    android:nestedScrollingEnabled="false" />

                <!-- Services Section -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Servicios Incluidos"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp" />

                <CheckBox
                    android:id="@+id/cb_breakfast"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Desayuno" />

                <CheckBox
                    android:id="@+id/cb_lunch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Almuerzo" />

                <CheckBox
                    android:id="@+id/cb_dinner"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Cena" />

                <CheckBox
                    android:id="@+id/cb_transport"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Transporte"
                    android:layout_marginBottom="24dp" />

                <!-- Languages Section -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Idiomas Disponibles"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp" />

                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/chip_group_languages"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_spanish"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Español"
                        android:checkable="true"
                        android:checked="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_english"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Inglés"
                        android:checkable="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_french"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Francés"
                        android:checkable="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_german"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Alemán"
                        android:checkable="true" />

                </com.google.android.material.chip.ChipGroup>

                <!-- Action Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="end">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_cancel"
                        style="@style/Widget.Material3.Button.TextButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/cancel"
                        android:layout_marginEnd="16dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_save_tour"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/save" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
