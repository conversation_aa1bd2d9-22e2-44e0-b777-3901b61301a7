<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header con estado -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_tour_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="City Tour Centro Histórico"
                android:textStyle="bold"
                android:textSize="16sp"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/tv_reservation_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="CONFIRMADA"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/green"
                android:background="@drawable/status_background_green"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp" />

        </LinearLayout>

        <!-- Información del tour -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_company_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tours Cusco Adventures"
                    android:textSize="14sp"
                    android:textColor="@color/gray" />

                <TextView
                    android:id="@+id/tv_tour_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="15 Dic 2024 - 09:00 AM"
                    android:textSize="14sp"
                    android:textColor="@color/gray" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_payment_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="S/. 120"
                android:textStyle="bold"
                android:textSize="18sp"
                android:textColor="@color/primary" />

        </LinearLayout>

        <!-- Botones de acción -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_view_qr"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Ver QRs"
                android:layout_marginEnd="8dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_cancel_reservation"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Cancelar"
                android:layout_marginStart="8dp"
                style="@style/Widget.Material3.Button.TextButton" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
