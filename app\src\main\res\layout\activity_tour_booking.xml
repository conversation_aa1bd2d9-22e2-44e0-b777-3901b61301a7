<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Reservar Tour"
            app:titleTextColor="@color/white"
            app:navigationIcon="@android:drawable/ic_menu_revert"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Tour Details Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_tour_details"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:layout_constraintTop_toBottomOf="@id/toolbar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <ImageView
                    android:id="@+id/iv_tour_image"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:src="@android:drawable/ic_menu_mapmode"
                    android:background="@color/light_gray"
                    android:scaleType="centerCrop" />

                <TextView
                    android:id="@+id/tv_tour_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tour Machu Picchu Completo"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_marginTop="12dp" />

                <TextView
                    android:id="@+id/tv_company_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tours Cusco Adventures"
                    android:textSize="14sp"
                    android:textColor="@color/primary"
                    android:layout_marginTop="4dp" />

                <TextView
                    android:id="@+id/tv_tour_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Descubre la maravilla del mundo con nuestro tour completo que incluye transporte, guía profesional y almuerzo típico."
                    android:textSize="14sp"
                    android:textColor="@color/gray"
                    android:layout_marginTop="8dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Duración: 8 horas"
                        android:textSize="12sp"
                        android:textColor="@color/gray" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Idioma: Español/Inglés"
                        android:textSize="12sp"
                        android:textColor="@color/gray"
                        android:gravity="end" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_tour_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="S/. 250.00 por persona"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/green"
                    android:layout_marginTop="12dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Booking Form Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_booking_form"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:layout_constraintTop_toBottomOf="@id/card_tour_details">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Detalles de la Reserva"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_tour_date"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Fecha del Tour"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_tour_date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="date"
                        android:focusable="false"
                        android:clickable="true" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_participants"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Número de Participantes"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_participants"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:text="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_special_requests"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Solicitudes Especiales (Opcional)"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_special_requests"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:lines="3" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Payment Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_payment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:layout_constraintTop_toBottomOf="@id/card_booking_form">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Información de Pago"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_card_number"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Número de Tarjeta"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_card_number"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number" />

                </com.google.android.material.textfield.TextInputLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_expiry_date"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:hint="MM/AA">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_expiry_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="date" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_cvv"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:hint="CVV">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_cvv"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLength="3" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_cardholder_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Nombre del Titular"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_cardholder_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPersonName" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Total -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:background="@color/light_gray"
                    android:padding="12dp"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Total a Pagar:"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/black" />

                    <TextView
                        android:id="@+id/tv_total_amount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/. 250.00"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/green" />

                </LinearLayout>

                <!-- Book Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_book_tour"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="@string/make_reservation"
                    android:textSize="16sp"
                    app:cornerRadius="8dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
