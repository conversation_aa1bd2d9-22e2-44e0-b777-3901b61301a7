<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface">

    <!-- Main Content -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            android:elevation="4dp"
            app:title="Administrador de Empresa"
            app:titleTextColor="?attr/colorOnPrimary"
            app:navigationIcon="@drawable/ic_menu_24"
            app:navigationIconTint="?attr/colorOnPrimary"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Dashboard Content -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:padding="16dp"
            android:background="?attr/colorSurface"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            app:layout_constraintBottom_toBottomOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Welcome Header -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="@drawable/bg_section_header"
                    android:padding="20dp"
                    android:layout_marginBottom="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="¡Bienvenido!"
                        android:textAppearance="?attr/textAppearanceHeadlineSmall"
                        android:textColor="?attr/colorOnPrimaryContainer" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Gestiona tu empresa de turismo"
                        android:textAppearance="?attr/textAppearanceBodyMedium"
                        android:textColor="?attr/colorOnPrimaryContainer"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

                <!-- Company Info Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_company_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:rippleColor="?attr/colorPrimary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="20dp"
                        android:gravity="center_vertical"
                        android:minHeight="100dp">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="56dp"
                            android:layout_height="56dp"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="0dp"
                            app:cardBackgroundColor="?attr/colorPrimaryContainer">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_business_24"
                                android:tint="?attr/colorOnPrimaryContainer" />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Información de Empresa"
                                android:textAppearance="?attr/textAppearanceTitleMedium"
                                android:textColor="?attr/colorOnSurface" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Gestionar información de la empresa"
                                android:textAppearance="?attr/textAppearanceBodyMedium"
                                android:textColor="?attr/colorOnSurfaceVariant"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_arrow_forward_24"
                            android:tint="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Service Management Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_service_management"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:rippleColor="?attr/colorSecondary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="20dp"
                        android:gravity="center_vertical"
                        android:minHeight="100dp">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="56dp"
                            android:layout_height="56dp"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="0dp"
                            app:cardBackgroundColor="?attr/colorSecondaryContainer">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_services_24"
                                android:tint="?attr/colorOnSecondaryContainer" />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Gestión de Servicios"
                                android:textAppearance="?attr/textAppearanceTitleMedium"
                                android:textColor="?attr/colorOnSurface" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Crear y gestionar servicios"
                                android:textAppearance="?attr/textAppearanceBodyMedium"
                                android:textColor="?attr/colorOnSurfaceVariant"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_arrow_forward_24"
                            android:tint="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Tour Management Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_tour_management"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:rippleColor="?attr/colorTertiary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="20dp"
                        android:gravity="center_vertical"
                        android:minHeight="100dp">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="56dp"
                            android:layout_height="56dp"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="0dp"
                            app:cardBackgroundColor="?attr/colorTertiaryContainer">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_tour_24"
                                android:tint="?attr/colorOnTertiaryContainer" />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Gestión de Tours"
                                android:textAppearance="?attr/textAppearanceTitleMedium"
                                android:textColor="?attr/colorOnSurface" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Crear y gestionar tours"
                                android:textAppearance="?attr/textAppearanceBodyMedium"
                                android:textColor="?attr/colorOnSurfaceVariant"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_arrow_forward_24"
                            android:tint="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Guide Management Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_guide_management"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:rippleColor="?attr/colorPrimary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="20dp"
                        android:gravity="center_vertical"
                        android:minHeight="100dp">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="56dp"
                            android:layout_height="56dp"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="0dp"
                            app:cardBackgroundColor="?attr/colorPrimaryContainer">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_guide_24"
                                android:tint="?attr/colorOnPrimaryContainer" />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Gestión de Guías"
                                android:textAppearance="?attr/textAppearanceTitleMedium"
                                android:textColor="?attr/colorOnSurface" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Gestionar guías de turismo"
                                android:textAppearance="?attr/textAppearanceBodyMedium"
                                android:textColor="?attr/colorOnSurfaceVariant"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_arrow_forward_24"
                            android:tint="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Guide Tracking Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_guide_tracking"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:rippleColor="?attr/colorSecondary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="20dp"
                        android:gravity="center_vertical"
                        android:minHeight="100dp">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="56dp"
                            android:layout_height="56dp"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="0dp"
                            app:cardBackgroundColor="?attr/colorSecondaryContainer">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_location_tracking_24"
                                android:tint="?attr/colorOnSecondaryContainer" />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Seguimiento de Guías"
                                android:textAppearance="?attr/textAppearanceTitleMedium"
                                android:textColor="?attr/colorOnSurface" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Ver ubicación en tiempo real"
                                android:textAppearance="?attr/textAppearanceBodyMedium"
                                android:textColor="?attr/colorOnSurfaceVariant"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_arrow_forward_24"
                            android:tint="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Checkout Alerts Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_checkout_alerts"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:rippleColor="?attr/colorTertiary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="20dp"
                        android:gravity="center_vertical"
                        android:minHeight="100dp">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="56dp"
                            android:layout_height="56dp"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="0dp"
                            app:cardBackgroundColor="?attr/colorTertiaryContainer">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_notifications_24"
                                android:tint="?attr/colorOnTertiaryContainer" />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Alertas Check-out"
                                android:textAppearance="?attr/textAppearanceTitleMedium"
                                android:textColor="?attr/colorOnSurface" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Procesar pagos de tours"
                                android:textAppearance="?attr/textAppearanceBodyMedium"
                                android:textColor="?attr/colorOnSurfaceVariant"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="0dp"
                            app:cardBackgroundColor="@color/orange"
                            android:layout_marginEnd="12dp">

                            <TextView
                                android:id="@+id/tv_pending_alerts_count"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:text="3"
                                android:textAppearance="?attr/textAppearanceLabelMedium"
                                android:textColor="@color/white"
                                android:gravity="center" />

                        </com.google.android.material.card.MaterialCardView>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_arrow_forward_24"
                            android:tint="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Sales Reports Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_sales_reports"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:rippleColor="?attr/colorPrimary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="20dp"
                        android:gravity="center_vertical"
                        android:minHeight="100dp">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="56dp"
                            android:layout_height="56dp"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="0dp"
                            app:cardBackgroundColor="?attr/colorPrimaryContainer">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_reports_24"
                                android:tint="?attr/colorOnPrimaryContainer" />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Reportes de Ventas"
                                android:textAppearance="?attr/textAppearanceTitleMedium"
                                android:textColor="?attr/colorOnSurface" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Ver reportes de ventas"
                                android:textAppearance="?attr/textAppearanceBodyMedium"
                                android:textColor="?attr/colorOnSurfaceVariant"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_arrow_forward_24"
                            android:tint="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Customer Chat Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_customer_chat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:rippleColor="?attr/colorSecondary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="20dp"
                        android:gravity="center_vertical"
                        android:minHeight="100dp">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="56dp"
                            android:layout_height="56dp"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="0dp"
                            app:cardBackgroundColor="?attr/colorSecondaryContainer">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_chat_24"
                                android:tint="?attr/colorOnSecondaryContainer" />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Chat con Clientes"
                                android:textAppearance="?attr/textAppearanceTitleMedium"
                                android:textColor="?attr/colorOnSurface" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Atención al cliente en tiempo real"
                                android:textAppearance="?attr/textAppearanceBodyMedium"
                                android:textColor="?attr/colorOnSurfaceVariant"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="0dp"
                            app:cardBackgroundColor="@color/green"
                            android:layout_marginEnd="12dp">

                            <TextView
                                android:id="@+id/tv_active_chats_count"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:text="2"
                                android:textAppearance="?attr/textAppearanceLabelMedium"
                                android:textColor="@color/white"
                                android:gravity="center" />

                        </com.google.android.material.card.MaterialCardView>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_arrow_forward_24"
                            android:tint="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </ScrollView>

        <!-- FAB for Create Tour -->
        <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
            android:id="@+id/fab_create_tour"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:text="Crear Tour"
            android:textAppearance="?attr/textAppearanceLabelLarge"
            app:icon="@drawable/ic_add_24"
            app:iconTint="?attr/colorOnPrimary"
            android:contentDescription="Crear nuevo tour"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        app:menu="@menu/tour_admin_nav_menu" />

</androidx.drawerlayout.widget.DrawerLayout>
