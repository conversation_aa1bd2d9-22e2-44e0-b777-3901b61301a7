<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Main Content -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="@string/tour_admin"
            app:titleTextColor="@color/white"
            app:navigationIcon="@android:drawable/ic_menu_sort_alphabetically"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Dashboard Content -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:padding="16dp"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            app:layout_constraintBottom_toBottomOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Company Info Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_company_info"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@android:drawable/ic_menu_info_details"
                            android:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/company_info"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Gestionar información de la empresa"
                                android:textSize="14sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_more"
                            android:tint="@color/gray" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Service Management Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_service_management"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@android:drawable/ic_menu_help"
                            android:tint="@color/accent" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Gestión de Servicios"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Crear y gestionar servicios"
                                android:textSize="14sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_more"
                            android:tint="@color/gray" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Tour Management Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_tour_management"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@android:drawable/ic_menu_mapmode"
                            android:tint="@color/green" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/tour_management"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Crear y gestionar tours"
                                android:textSize="14sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_more"
                            android:tint="@color/gray" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Guide Management Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_guide_management"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@android:drawable/ic_menu_myplaces"
                            android:tint="@color/orange" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/guide_management"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Gestionar guías de turismo"
                                android:textSize="14sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_more"
                            android:tint="@color/gray" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Guide Tracking Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_guide_tracking"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@android:drawable/ic_menu_mylocation"
                            android:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Seguimiento de Guías"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Ver ubicación en tiempo real"
                                android:textSize="14sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_more"
                            android:tint="@color/gray" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Checkout Alerts Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_checkout_alerts"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@android:drawable/ic_menu_agenda"
                            android:tint="@color/orange" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Alertas Check-out"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Procesar pagos de tours"
                                android:textSize="14sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_pending_alerts_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="3"
                            android:textSize="14sp"
                            android:textColor="@color/white"
                            android:background="@drawable/circle_orange"
                            android:gravity="center"
                            android:minWidth="28dp"
                            android:minHeight="28dp"
                            android:layout_marginEnd="8dp" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_more"
                            android:tint="@color/gray" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Sales Reports Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_sales_reports"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@android:drawable/ic_menu_report_image"
                            android:tint="@color/accent" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/sales_reports"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Ver reportes de ventas"
                                android:textSize="14sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_more"
                            android:tint="@color/gray" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Customer Chat Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_customer_chat"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@android:drawable/ic_menu_help"
                            android:tint="@color/green" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Chat con Clientes"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Atención al cliente en tiempo real"
                                android:textSize="14sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_active_chats_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="2"
                            android:textSize="14sp"
                            android:textColor="@color/white"
                            android:background="@drawable/circle_green"
                            android:gravity="center"
                            android:minWidth="28dp"
                            android:minHeight="28dp"
                            android:layout_marginEnd="8dp" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_more"
                            android:tint="@color/gray" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </ScrollView>

        <!-- FAB for Create Tour -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab_create_tour"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:src="@android:drawable/ic_input_add"
            android:contentDescription="@string/create_tour"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        app:menu="@menu/tour_admin_nav_menu" />

</androidx.drawerlayout.widget.DrawerLayout>
