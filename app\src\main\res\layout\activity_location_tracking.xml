<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Registrar Ubicación"
        app:titleTextColor="@color/white"
        app:navigationIcon="@android:drawable/ic_menu_revert"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Tour Info Header -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_tour_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/primary"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@android:drawable/ic_menu_mapmode"
                android:tint="@color/white" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="16dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_tour_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="City Tour Lima Centro"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:singleLine="true" />

                <TextView
                    android:id="@+id/tv_tour_progress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Punto 2 de 4 • 6 participantes"
                    android:textSize="14sp"
                    android:textColor="@color/white"
                    android:layout_marginTop="4dp"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:singleLine="true" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_current_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="10:30 AM"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/white" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Map Container -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_map"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintTop_toBottomOf="@id/card_tour_info"
        app:layout_constraintBottom_toTopOf="@id/card_location_points"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintHeight_percent="0.48">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@color/light_gray">

            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:src="@android:drawable/ic_menu_mapmode"
                android:tint="@color/primary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Mapa del Tour"
                android:textSize="16sp"
                android:textColor="@color/primary"
                android:textStyle="bold"
                android:layout_marginTop="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Tu ubicación actual y puntos del tour"
                android:textSize="14sp"
                android:textColor="@color/gray"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:textAlignment="center" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_center_location"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Centrar ubicación"
                android:drawableStart="@android:drawable/ic_menu_mylocation"
                android:layout_marginTop="16dp"
                android:maxWidth="200dp"
                android:ellipsize="end"
                android:singleLine="true" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Location Points List -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_location_points"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintVertical_bias="1"
        app:layout_constraintHeight_percent="0.36">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!-- Header -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="16dp"
                android:background="@color/green">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_mylocation"
                    android:tint="@color/white" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Puntos del Tour"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:layout_marginStart="12dp" />

                <TextView
                    android:id="@+id/tv_points_completed"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2/4 completados"
                    android:textSize="14sp"
                    android:textColor="@color/white" />

            </LinearLayout>

            <!-- Points List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_location_points"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingBottom="8dp"
                android:clipToPadding="false" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Register Location FAB -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_register_location"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:src="@android:drawable/ic_menu_mylocation"
        android:contentDescription="Registrar ubicación actual"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
