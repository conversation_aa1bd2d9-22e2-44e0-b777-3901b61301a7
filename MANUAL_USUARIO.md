# Manual de Usuario - DroidTour

## Introducción

DroidTour es una aplicación móvil para la gestión de reservas de tours locales que conecta a clientes, guías de turismo, empresas turísticas y superadministradores en una plataforma integral.

## Primeros Pasos

### Instalación
1. Descargar la aplicación desde Google Play Store
2. Instalar en dispositivo Android 12.0 o superior
3. Abrir la aplicación

### Registro de Usuario
1. En la pantalla principal, tocar "Iniciar Sesión"
2. Seleccionar "Registrarse"
3. Elegir el tipo de usuario:
   - **Cliente**: Para reservar y disfrutar tours
   - **Guía de Turismo**: Para ofrecer servicios de guía
   - **Administrador de Empresa**: Para gestionar empresa turística

## Guía por Tipo de Usuario

---

## 👤 CLIENTE

### Registro
1. Completar datos personales:
   - Nombres y apellidos
   - Tipo y número de documento
   - Fecha de nacimiento
   - Email y teléfono
   - Domicilio
   - Foto de perfil
2. Crear contraseña segura
3. Confirmar registro

### Funcionalidades Principales

#### 🏠 Dashboard Principal
- **Barra de búsqueda**: Buscar empresas o tours específicos
- **Botones de acceso rápido**:
  - "Mis Reservas": Ver reservas activas
  - "Historial": Tours completados
  - "Chat Soporte": Comunicación con empresas
  - "Seguimiento": Ubicación en tiempo real del tour
- **Lista de empresas**: Explorar empresas turísticas disponibles

#### 📅 Realizar Reserva
1. Seleccionar empresa de la lista
2. Elegir tour disponible
3. Completar formulario:
   - Fecha preferida
   - Número de participantes
   - Información de tarjeta de crédito/débito
4. Confirmar reserva
5. Recibir confirmación por email

#### 📱 Mis Reservas
- Ver reservas confirmadas y pendientes
- Estados: "CONFIRMADA" (verde), "PENDIENTE" (naranja)
- Acciones disponibles:
  - **Ver QRs**: Acceder a códigos QR de inicio y fin
  - **Cancelar**: Cancelar reserva (según políticas)

#### 🔍 QR Check-in/Check-out
- **QR de Inicio**: Mostrar al guía al comenzar el tour
- **QR de Fin**: Mostrar al guía al finalizar el tour
- Después del QR de fin: valorar el tour automáticamente

#### 📊 Historial de Tours
- Ver tours completados con estadísticas:
  - 8 tours completados
  - Promedio de valoración: 4.2 ⭐
- Acciones por tour:
  - **Valorar Tour**: Calificar experiencia (1-5 estrellas)
  - **Ver Detalles**: Información completa del tour

#### ⭐ Valorar Tours
1. Seleccionar calificación (1-5 estrellas)
2. Escribir comentarios y observaciones
3. Enviar valoración
4. La valoración se refleja en el perfil de la empresa

#### 💬 Chat con Empresas
- Lista de conversaciones activas con empresas
- Indicadores de mensajes no leídos
- Soporte directo para dudas sobre reservas
- Historial de conversaciones

#### 🗺️ Seguimiento en Tiempo Real
- **Mapa en vivo**: Ubicación actual del tour
- **Estado actual**:
  - Ubicación actual del grupo
  - Próximo destino
  - Tiempo estimado de llegada
  - Número de participantes
- **Progreso del tour**: Lista de puntos visitados/por visitar
- **Botones de acción**:
  - Centrar mapa en ubicación
  - Contactar al guía directamente

---

## 🧭 GUÍA DE TURISMO

### Registro
1. Completar perfil personal (igual que cliente)
2. Información adicional:
   - Idiomas que habla
   - Experiencia en turismo
   - Especialidades
3. **Nota**: Requiere aprobación del Superadministrador

### Funcionalidades Principales

#### 🏠 Dashboard Principal
- Acceso rápido a ofertas de tours
- Mis tours activos
- Escáner QR para check-in/out
- Registro de ubicaciones GPS

#### 💼 Ofertas de Tours
- Ver ofertas de empresas turísticas
- Filtros por:
  - Fecha
  - Tipo de tour
  - Pago ofrecido
- **Acciones**:
  - Aceptar oferta
  - Denegar oferta
  - Ver detalles completos

#### 📅 Mis Tours Activos
- Tours confirmados y asignados
- Resumen diario:
  - Tours de hoy
  - Próximos tours
  - Historial
  - Ganancias del día
- Estados: Hoy, Próximos, Historial

#### 📍 Registrar Ubicación
- **Mapa interactivo**: Ubicación actual en el mapa
- **Información del tour**:
  - Nombre del tour
  - Progreso actual
- **Puntos de registro**:
  - Lista de ubicaciones a visitar
  - Registrar llegada a cada punto
  - GPS automático al registrar
- **Botón**: Centrar mi ubicación

#### 📷 Escáner QR
- **Check-in**: Escanear QR de inicio del cliente
- **Check-out**: Escanear QR de fin del cliente
- Confirmación automática de inicio/finalización
- Notificación a la empresa para procesar pago

---

## 🏢 ADMINISTRADOR DE EMPRESA

### Registro
1. **Datos personales** (como cliente)
2. **Datos de la empresa**:
   - Nombre de la empresa
   - Email y teléfono corporativo
   - Ubicación física
   - Fotos promocionales (mínimo 2)

### Funcionalidades Principales

#### 🏠 Dashboard Empresarial
- Resumen de ventas y reservas
- Tours activos
- Gestión de guías
- Chat con clientes

#### 🎯 Crear Tours
1. **Información básica**:
   - Nombre y descripción
   - Duración del tour
   - Costo por persona
2. **Itinerario**:
   - Lugares a visitar (con ubicación en mapa)
   - Actividades en cada lugar
   - Tiempos estimados
3. **Servicios incluidos**:
   - Desayuno, almuerzo, etc.
   - Costos adicionales
4. **Configuración**:
   - Idiomas ofrecidos
   - Fechas de inicio y fin
   - Capacidad máxima

#### 👥 Gestión de Guías
- **Buscar guías**: Filtros por idioma, experiencia, disponibilidad
- **Propuestas de trabajo**:
  - Seleccionar guía para tour específico
  - Enviar propuesta con pago ofrecido
  - Esperar aceptación/rechazo
- **Seguimiento de guías**:
  - Ver ubicación en tiempo real
  - Estado de tours asignados

#### 💬 Chat con Clientes
- **Lista de conversaciones**: Organizadas por cliente
- **Estadísticas**:
  - 3 chats activos
  - 2 chats pendientes
  - Tiempo promedio de respuesta: 2.5 min
- **Gestión de consultas**:
  - Responder dudas sobre tours
  - Confirmar reservas
  - Soporte post-venta

#### 🚨 Alertas de Check-out
- **Notificaciones automáticas**: Cuando cliente completa tour
- **Acciones disponibles**:
  - Procesar pago de tarjeta registrada
  - Confirmar finalización de servicio
  - Generar factura

#### 📊 Reportes de Ventas
- **Por servicio** (menor a mayor):
  - Lista de tours por volumen de ventas
  - Análisis de rentabilidad
- **Por tour** (mayor a menor):
  - Tours más populares
  - Ingresos por tour
- **Filtros temporales**:
  - Diario, mensual, anual

---

## 👑 SUPERADMINISTRADOR

### Funcionalidades Principales

#### 👥 Gestión de Usuarios
- **Administradores de empresa**:
  - Aprobar/rechazar registros
  - Activar/desactivar cuentas
- **Guías de turismo**:
  - Habilitar guías registrados
  - Gestionar permisos
- **Clientes**:
  - Monitorear actividad
  - Soporte técnico

#### 📈 Reportes del Sistema
- **Reservas por empresa**: Análisis comparativo
- **Estadísticas generales**: Usuarios activos, tours realizados
- **Métricas de rendimiento**: Tiempos de respuesta, satisfacción

#### 📋 Logs del Sistema
- **Registro de eventos**: Todas las acciones importantes
- **Auditoría**: Seguimiento de cambios críticos
- **Monitoreo**: Detección de problemas técnicos

---

## ⚙️ Configuraciones Generales

### Notificaciones
- Confirmaciones de reserva
- Recordatorios de tours
- Mensajes de chat
- Alertas de check-out

### Seguridad
- Cambiar contraseña
- Verificación en dos pasos (próximamente)
- Cerrar sesión en todos los dispositivos

### Soporte
- Chat con soporte técnico
- Preguntas frecuentes
- Reportar problemas

---

## 🆘 Solución de Problemas

### Problemas Comunes

#### No puedo iniciar sesión
1. Verificar email y contraseña
2. Usar "¿Olvidaste tu contraseña?"
3. Verificar conexión a internet

#### No aparecen las empresas
1. Verificar conexión a internet
2. Actualizar la aplicación
3. Reiniciar la app

#### El QR no funciona
1. Verificar que la cámara tenga permisos
2. Asegurar buena iluminación
3. Limpiar la cámara del dispositivo

#### No recibo notificaciones
1. Verificar permisos de notificaciones
2. Revisar configuración de la app
3. Verificar configuración del dispositivo

### Contacto de Soporte
- **Email**: <EMAIL>
- **Chat**: Disponible en la aplicación
- **Horario**: Lunes a Viernes, 9:00 AM - 6:00 PM

---

## 📋 Credenciales de Prueba

Para pruebas de la aplicación:

- **Superadmin**: `<EMAIL>` / `admin123`
- **Admin Empresa**: `<EMAIL>` / `admin123`
- **Guía**: `<EMAIL>` / `guia123`
- **Cliente**: `<EMAIL>` / `cliente123`

---

*Última actualización: Diciembre 2024*
*Versión: 1.0.0*
