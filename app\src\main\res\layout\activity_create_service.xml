<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Crear Servicio"
            app:titleTextColor="@color/white"
            app:navigationIcon="@android:drawable/ic_menu_revert"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Service Form -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_service_form"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:layout_constraintTop_toBottomOf="@id/toolbar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <!-- Service Images Section -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Imágenes del Servicio"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="24dp">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_service_image1"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="2dp"
                        android:clickable="true"
                        android:focusable="true">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:background="@color/light_gray">

                            <ImageView
                                android:id="@+id/iv_service_image1"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:src="@android:drawable/ic_menu_camera"
                                android:tint="@color/gray" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Imagen 1"
                                android:textSize="12sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="8dp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_service_image2"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="2dp"
                        android:clickable="true"
                        android:focusable="true">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:background="@color/light_gray">

                            <ImageView
                                android:id="@+id/iv_service_image2"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:src="@android:drawable/ic_menu_camera"
                                android:tint="@color/gray" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Imagen 2"
                                android:textSize="12sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="8dp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <!-- Service Basic Info Section -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Información del Servicio"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_service_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Nombre del Servicio"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_service_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_service_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Descripción del Servicio"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_service_description"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:lines="4" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_service_price"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Precio del Servicio"
                    android:layout_marginBottom="24dp"
                    app:prefixText="S/. ">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_service_price"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="numberDecimal" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Services Included Section -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Servicios Incluidos"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp" />

                <CheckBox
                    android:id="@+id/cb_breakfast"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Desayuno" />

                <CheckBox
                    android:id="@+id/cb_lunch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Almuerzo" />

                <CheckBox
                    android:id="@+id/cb_dinner"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Cena" />

                <CheckBox
                    android:id="@+id/cb_transport"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Transporte"
                    android:layout_marginBottom="24dp" />

                <!-- Action Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="end">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_cancel_service"
                        style="@style/Widget.Material3.Button.TextButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/cancel"
                        android:layout_marginEnd="16dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_save_service"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Guardar Servicio" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
