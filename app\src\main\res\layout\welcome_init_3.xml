<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <ImageView
        android:id="@+id/imageninit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/welcome_ubi"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.497"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mensaje1"
        app:layout_constraintVertical_bias="0.172" />

    <TextView
        android:id="@+id/saltar"
        android:layout_width="93dp"
        android:layout_height="40dp"
        android:layout_alignParentTop="true"
        android:fontFamily="@font/bricolage_grotesque"
        android:text="@string/saltar"
        android:textAlignment="center"
        android:textAllCaps="false"
        android:textColor="#FE003B95"
        android:textSize="21sp"
        app:layout_constraintBottom_toTopOf="@+id/mensaje1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.949"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/mensaje1"
        android:layout_width="328dp"
        android:layout_height="81dp"
        android:fontFamily="@font/bricolage_grotesque"
        android:gravity="center"
        android:text="@string/mensaje3"
        android:textAlignment="center"
        android:textColor="#FE003B95"
        android:textSize="27sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.495"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.096" />

    <TextView
        android:id="@+id/mensaje2"
        android:layout_width="347dp"
        android:layout_height="98dp"
        android:fontFamily="@font/bricolage_grotesque"
        android:gravity="center"
        android:text="@string/mensaje4"
        android:textAlignment="center"
        android:textColor="#FE003B95"
        android:textSize="25sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imageninit"
        app:layout_constraintVertical_bias="0.339" />

</androidx.constraintlayout.widget.ConstraintLayout>