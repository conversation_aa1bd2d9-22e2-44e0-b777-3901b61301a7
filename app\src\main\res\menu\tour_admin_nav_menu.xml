<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android">
    <group android:checkableBehavior="single">
        <item
            android:id="@+id/nav_dashboard"
            android:icon="@android:drawable/ic_menu_view"
            android:title="Dashboard" />
        
        <item
            android:id="@+id/nav_company_info"
            android:icon="@android:drawable/ic_menu_info_details"
            android:title="Información Empresa" />
        
        <item
            android:id="@+id/nav_tour_management"
            android:icon="@android:drawable/ic_menu_mapmode"
            android:title="Gestión Tours" />
        
        <item
            android:id="@+id/nav_guide_management"
            android:icon="@android:drawable/ic_menu_myplaces"
            android:title="Gestión Guías" />
        
        <item
            android:id="@+id/nav_guide_tracking"
            android:icon="@android:drawable/ic_menu_mylocation"
            android:title="Seguimiento Guías" />
        
        <item
            android:id="@+id/nav_checkout_alerts"
            android:icon="@android:drawable/ic_menu_agenda"
            android:title="Alertas Check-out" />
        
        <item
            android:id="@+id/nav_sales_reports"
            android:icon="@android:drawable/ic_menu_report_image"
            android:title="Reportes Ventas" />
        
        <item
            android:id="@+id/nav_customer_chat"
            android:icon="@android:drawable/ic_menu_help"
            android:title="Chat Clientes" />
    </group>
    
    <group android:id="@+id/group_settings">
        <item
            android:id="@+id/nav_settings"
            android:icon="@android:drawable/ic_menu_preferences"
            android:title="Configuración" />
        
        <item
            android:id="@+id/nav_logout"
            android:icon="@android:drawable/ic_menu_revert"
            android:title="Cerrar Sesión" />
    </group>
</menu>