<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="@string/rate_tour"
            app:titleTextColor="@color/white"
            app:navigationIcon="@android:drawable/ic_menu_revert"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Rating Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_rating"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:layout_constraintTop_toBottomOf="@id/toolbar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="¿Cómo fue tu experiencia?"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:id="@+id/tv_tour_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tour Machu Picchu Completo"
                    android:textSize="16sp"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="24dp" />

                <!-- Star Rating -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="24dp">

                    <ImageView
                        android:id="@+id/star1"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@android:drawable/btn_star_big_off"
                        android:layout_marginHorizontal="4dp"
                        android:clickable="true"
                        android:focusable="true" />

                    <ImageView
                        android:id="@+id/star2"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@android:drawable/btn_star_big_off"
                        android:layout_marginHorizontal="4dp"
                        android:clickable="true"
                        android:focusable="true" />

                    <ImageView
                        android:id="@+id/star3"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@android:drawable/btn_star_big_off"
                        android:layout_marginHorizontal="4dp"
                        android:clickable="true"
                        android:focusable="true" />

                    <ImageView
                        android:id="@+id/star4"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@android:drawable/btn_star_big_off"
                        android:layout_marginHorizontal="4dp"
                        android:clickable="true"
                        android:focusable="true" />

                    <ImageView
                        android:id="@+id/star5"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@android:drawable/btn_star_big_off"
                        android:layout_marginHorizontal="4dp"
                        android:clickable="true"
                        android:focusable="true" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_rating_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Toca las estrellas para calificar"
                    android:textSize="14sp"
                    android:textColor="@color/gray"
                    android:layout_marginBottom="16dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Comments Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_comments"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:layout_constraintTop_toBottomOf="@id/card_rating">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Comparte tu experiencia"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_comments"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Cuéntanos sobre tu experiencia..."
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_comments"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:lines="4"
                        android:gravity="top" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Rating Categories -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Califica aspectos específicos"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_marginBottom="16dp" />

                <!-- Guide Rating -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Guía Turístico:"
                        android:textSize="14sp"
                        android:textColor="@color/black" />

                    <RatingBar
                        android:id="@+id/rating_guide"
                        style="@style/Widget.AppCompat.RatingBar.Small"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:numStars="5"
                        android:rating="0" />

                </LinearLayout>

                <!-- Service Rating -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Servicio:"
                        android:textSize="14sp"
                        android:textColor="@color/black" />

                    <RatingBar
                        android:id="@+id/rating_service"
                        style="@style/Widget.AppCompat.RatingBar.Small"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:numStars="5"
                        android:rating="0" />

                </LinearLayout>

                <!-- Value Rating -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="24dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Relación Calidad-Precio:"
                        android:textSize="14sp"
                        android:textColor="@color/black" />

                    <RatingBar
                        android:id="@+id/rating_value"
                        style="@style/Widget.AppCompat.RatingBar.Small"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:numStars="5"
                        android:rating="0" />

                </LinearLayout>

                <!-- Submit Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_submit_rating"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="Enviar Valoración"
                    android:textSize="16sp"
                    app:cornerRadius="8dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
