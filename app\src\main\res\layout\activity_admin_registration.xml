<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/light_gray">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Registro de Administrador"
        app:titleTextColor="@color/white"
        app:navigationIcon="@android:drawable/ic_menu_revert" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- <PERSON><PERSON> Personales -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Datos Personales del Administrador"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginBottom="16dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Nombres"
                            android:layout_marginEnd="8dp">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_first_name"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="textPersonName" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Apellidos"
                            android:layout_marginStart="8dp">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_last_name"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="textPersonName" />

                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="12dp">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Tipo de Documento"
                            app:endIconMode="dropdown_menu"
                            android:layout_marginEnd="8dp">

                            <AutoCompleteTextView
                                android:id="@+id/spinner_document_type"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Número de Documento"
                            android:layout_marginStart="8dp">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_document_number"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="number" />

                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Fecha de Nacimiento"
                        android:layout_marginTop="12dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_birth_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            android:focusable="false"
                            android:clickable="true" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="12dp">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Correo Electrónico"
                            android:layout_marginEnd="8dp">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_email"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="textEmailAddress" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Teléfono"
                            android:layout_marginStart="8dp">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_phone"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="phone" />

                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Domicilio"
                        android:layout_marginTop="12dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_address"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:minLines="2" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_select_photo"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Seleccionar Foto Personal"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_marginTop="12dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Datos de la Empresa -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Datos de la Empresa de Turismo"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginBottom="16dp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Nombre de la Empresa"
                        android:layout_marginBottom="12dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_company_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Correo de la Empresa"
                            android:layout_marginEnd="8dp">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_company_email"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="textEmailAddress" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Teléfono de la Empresa"
                            android:layout_marginStart="8dp">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_company_phone"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="phone" />

                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Ubicación de la Empresa"
                        android:layout_marginTop="12dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_company_address"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:minLines="2" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_select_company_photos"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Seleccionar Fotos Promocionales (Mín. 2)"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_marginTop="12dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Datos de Cuenta -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Datos de Cuenta"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginBottom="16dp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Contraseña"
                        app:endIconMode="password_toggle"
                        android:layout_marginBottom="12dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_password"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPassword" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Confirmar Contraseña"
                        app:endIconMode="password_toggle"
                        android:layout_marginBottom="12dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_confirm_password"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPassword" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_register"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Registrar Administrador y Empresa"
                android:textSize="16sp"
                app:cornerRadius="8dp" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>
