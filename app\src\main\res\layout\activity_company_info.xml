<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="24dp">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            android:elevation="4dp"
            app:title="Información de la Empresa"
            app:titleTextColor="?attr/colorOnPrimary"
            app:navigationIcon="@drawable/ic_arrow_back_24"
            app:navigationIconTint="?attr/colorOnPrimary"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Company Info Form -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_company_form"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="2dp"
            app:strokeWidth="0dp"
            app:cardBackgroundColor="?attr/colorSurface"
            app:layout_constraintTop_toBottomOf="@id/toolbar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <!-- Company Logo/Images Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="@drawable/bg_section_header"
                    android:padding="16dp"
                    android:layout_marginBottom="20dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_image_24"
                        android:tint="?attr/colorOnPrimaryContainer"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Imágenes Promocionales"
                        android:textAppearance="?attr/textAppearanceTitleMedium"
                        android:textColor="?attr/colorOnPrimaryContainer" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="24dp">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_image1"
                        android:layout_width="0dp"
                        android:layout_height="140dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="12dp"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="1dp"
                        app:strokeWidth="2dp"
                        app:strokeColor="?attr/colorOutlineVariant"
                        android:clickable="true"
                        android:focusable="true"
                        app:rippleColor="?attr/colorPrimary">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:background="@drawable/bg_image_placeholder"
                            android:padding="16dp">

                            <ImageView
                                android:id="@+id/iv_company_image1"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:src="@drawable/ic_image_24"
                                android:tint="?attr/colorOnSurfaceVariant" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Imagen 1"
                                android:textAppearance="?attr/textAppearanceLabelMedium"
                                android:textColor="?attr/colorOnSurfaceVariant"
                                android:layout_marginTop="8dp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_image2"
                        android:layout_width="0dp"
                        android:layout_height="140dp"
                        android:layout_weight="1"
                        android:layout_marginStart="12dp"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="1dp"
                        app:strokeWidth="2dp"
                        app:strokeColor="?attr/colorOutlineVariant"
                        android:clickable="true"
                        android:focusable="true"
                        app:rippleColor="?attr/colorPrimary">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:background="@drawable/bg_image_placeholder"
                            android:padding="16dp">

                            <ImageView
                                android:id="@+id/iv_company_image2"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:src="@drawable/ic_image_24"
                                android:tint="?attr/colorOnSurfaceVariant" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Imagen 2"
                                android:textAppearance="?attr/textAppearanceLabelMedium"
                                android:textColor="?attr/colorOnSurfaceVariant"
                                android:layout_marginTop="8dp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <!-- Company Basic Info Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="@drawable/bg_section_header"
                    android:padding="16dp"
                    android:layout_marginBottom="20dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_business_24"
                        android:tint="?attr/colorOnPrimaryContainer"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Información Básica"
                        android:textAppearance="?attr/textAppearanceTitleMedium"
                        android:textColor="?attr/colorOnPrimaryContainer" />

                </LinearLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_company_name"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Nombre de la Empresa"
                    android:layout_marginBottom="20dp"
                    app:startIconDrawable="@drawable/ic_business_24"
                    app:startIconTint="?attr/colorOnSurfaceVariant">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_company_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:textAppearance="?attr/textAppearanceBodyLarge" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_company_email"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Correo Electrónico"
                    android:layout_marginBottom="20dp"
                    app:startIconDrawable="@android:drawable/ic_dialog_email"
                    app:startIconTint="?attr/colorOnSurfaceVariant">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_company_email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textEmailAddress"
                        android:textAppearance="?attr/textAppearanceBodyLarge" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_company_phone"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Teléfono"
                    android:layout_marginBottom="20dp"
                    app:startIconDrawable="@android:drawable/stat_sys_phone_call"
                    app:startIconTint="?attr/colorOnSurfaceVariant">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_company_phone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="phone"
                        android:textAppearance="?attr/textAppearanceBodyLarge" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Location Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="@drawable/bg_section_header"
                    android:padding="16dp"
                    android:layout_marginBottom="20dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_map_24"
                        android:tint="?attr/colorOnPrimaryContainer"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Ubicación"
                        android:textAppearance="?attr/textAppearanceTitleMedium"
                        android:textColor="?attr/colorOnPrimaryContainer" />

                </LinearLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_company_address"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Dirección"
                    android:layout_marginBottom="20dp"
                    app:startIconDrawable="@android:drawable/ic_dialog_map"
                    app:startIconTint="?attr/colorOnSurfaceVariant">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_company_address"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPostalAddress"
                        android:lines="2"
                        android:textAppearance="?attr/textAppearanceBodyLarge" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_select_location"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="Seleccionar Ubicación en Mapa"
                    android:textAppearance="?attr/textAppearanceLabelLarge"
                    app:icon="@drawable/ic_map_24"
                    app:iconGravity="start"
                    app:iconTint="?attr/colorPrimary"
                    android:layout_marginBottom="20dp"
                    app:cornerRadius="12dp" />

                <!-- Map Preview -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_map_preview"
                    android:layout_width="match_parent"
                    android:layout_height="220dp"
                    android:layout_marginBottom="32dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="?attr/colorOutlineVariant"
                    android:clickable="true"
                    android:focusable="true"
                    app:rippleColor="?attr/colorPrimary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:background="?attr/colorSurfaceVariant"
                        android:padding="24dp">

                        <ImageView
                            android:layout_width="64dp"
                            android:layout_height="64dp"
                            android:src="@drawable/ic_map_24"
                            android:tint="?attr/colorOnSurfaceVariant" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Vista previa del mapa"
                            android:textAppearance="?attr/textAppearanceBodyLarge"
                            android:textColor="?attr/colorOnSurfaceVariant"
                            android:layout_marginTop="12dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Toca para ver ubicación completa"
                            android:textAppearance="?attr/textAppearanceBodySmall"
                            android:textColor="?attr/colorOnSurfaceVariant"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Action Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="end"
                    android:layout_marginTop="8dp">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_cancel"
                        style="@style/Widget.Material3.Button.TextButton"
                        android:layout_width="wrap_content"
                        android:layout_height="48dp"
                        android:text="Cancelar"
                        android:textAppearance="?attr/textAppearanceLabelLarge"
                        android:layout_marginEnd="16dp"
                        app:cornerRadius="12dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_save_company"
                        style="@style/Widget.Material3.Button"
                        android:layout_width="wrap_content"
                        android:layout_height="48dp"
                        android:text="Guardar Información"
                        android:textAppearance="?attr/textAppearanceLabelLarge"
                        app:cornerRadius="12dp" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
