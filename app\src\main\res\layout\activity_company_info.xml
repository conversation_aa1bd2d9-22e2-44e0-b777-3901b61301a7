<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Información de la Empresa"
            app:titleTextColor="@color/white"
            app:navigationIcon="@android:drawable/ic_menu_revert"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Company Info Form -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_company_form"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:layout_constraintTop_toBottomOf="@id/toolbar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <!-- Company Logo/Images Section -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Imágenes Promocionales"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_image1"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="2dp"
                        android:clickable="true"
                        android:focusable="true">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:background="@color/light_gray">

                            <ImageView
                                android:id="@+id/iv_company_image1"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:src="@android:drawable/ic_menu_camera"
                                android:tint="@color/gray" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Imagen 1"
                                android:textSize="12sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="8dp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_image2"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="2dp"
                        android:clickable="true"
                        android:focusable="true">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:background="@color/light_gray">

                            <ImageView
                                android:id="@+id/iv_company_image2"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:src="@android:drawable/ic_menu_camera"
                                android:tint="@color/gray" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Imagen 2"
                                android:textSize="12sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="8dp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <!-- Company Basic Info Section -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Información Básica"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="16dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_company_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Nombre de la Empresa"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_company_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_company_email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Correo Electrónico"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_company_email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textEmailAddress" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_company_phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Teléfono"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_company_phone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="phone" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Location Section -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Ubicación"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="16dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_company_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Dirección"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_company_address"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPostalAddress"
                        android:lines="2" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_select_location"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Seleccionar Ubicación en Mapa"
                    android:drawableStart="@android:drawable/ic_menu_mapmode"
                    android:layout_marginBottom="16dp" />

                <!-- Map Preview -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_map_preview"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:layout_marginBottom="24dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:background="@color/light_gray">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@android:drawable/ic_menu_mapmode"
                            android:tint="@color/gray" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Vista previa del mapa"
                            android:textSize="14sp"
                            android:textColor="@color/gray"
                            android:layout_marginTop="8dp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Action Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="end">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_cancel"
                        style="@style/Widget.Material3.Button.TextButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/cancel"
                        android:layout_marginEnd="16dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_save_company"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Guardar Información" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
